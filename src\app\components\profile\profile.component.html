<app-breadcrumbs [items]="breadcrumbs"/>

<!-- Loading State -->
<div *ngIf="loading" class="max-w-screen-xl mb-5">
  <div class="flex justify-center items-center py-20">
    <mat-spinner diameter="50"></mat-spinner>
  </div>
</div>

<!-- Error State -->
<div *ngIf="error && !loading" class="max-w-screen-xl mb-5">
  <div class="bg-red-50 border border-red-200 rounded-lg p-4">
    <div class="flex items-center">
      <svg class="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
      </svg>
      <span class="text-red-800">{{ error }}</span>
    </div>
  </div>
</div>

<!-- Profile Content -->
<div *ngIf="!loading && !error && studentProfile" class="max-w-screen-xl mb-5">
  <div class="max-w-6xl">

    <!-- User Header -->
    <div class="flex justify-between gap-2 mb-4 border p-4 rounded-xl">
      <div class="flex items-center gap-2">
        <div class="rounded-full size-20">
          <img src="/assets/icons/profile.png" class="w-full rounded-full" alt="">
        </div>
        <div>
          <div class="text-lg font-semibold text-blue-500 capitalize">
            {{ studentProfile.user.name }} {{ studentProfile.user.surname }}
          </div>
          <div class="text-sm text-gray-500 font-medium flex gap-1">
            {{ studentProfile.user.role | titlecase }}
          </div>
          <div class="text-xs font-normal">Almaty, Narxoz</div>
        </div>
      </div>
      <div class="flex items-start">
        <button mat-icon-button
                (click)="refreshProfile()"
                [disabled]="loading"
                matTooltip="Refresh Profile"
                class="text-gray-600 hover:text-blue-600">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Academic Statistics -->
    <div class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Academic Statistics
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- GPA -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Current GPA</div>
          <div class="text-2xl font-bold" [ngClass]="getGpaColor(studentProfile.academic_stats.gpa)">
            {{ studentProfile.academic_stats.gpa | number:'1.2-2' }}
          </div>
        </div>

        <!-- Degree Progress -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Degree Progress</div>
          <div class="text-2xl font-bold text-blue-600">
            {{ studentProfile.academic_stats.degree_progress_percent | number:'1.1-1' }}%
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
            <div class="h-2 rounded-full"
                 [ngClass]="getProgressColor(studentProfile.academic_stats.degree_progress_percent)"
                 [style.width.%]="studentProfile.academic_stats.degree_progress_percent">
            </div>
          </div>
        </div>

        <!-- Credits Earned -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Credits Earned</div>
          <div class="text-2xl font-bold text-green-600">
            {{ studentProfile.academic_stats.total_credits_earned }}
          </div>
          <div class="text-xs text-gray-500">
            of {{ studentProfile.academic_stats.required_credits }} required
          </div>
        </div>

        <!-- Credits Attempted -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <div class="text-xs text-gray-600 mb-1">Credits Attempted</div>
          <div class="text-2xl font-bold text-purple-600">
            {{ studentProfile.academic_stats.total_credits_attempted }}
          </div>
        </div>
      </div>
    </div>

    <!-- Personal Information -->
    <div class="mb-4 border p-4 py-6 rounded-xl">
      <div class="flex justify-between">
        <div class="w-9/12">
          <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
            <img src="/assets/icons/info.svg" width="20" class="mb-0.5" alt="">
            Personal Information
          </div>
          <div class="mt-2 gap-y-5 grid md:grid-cols-2 grid-cols-1 text-xs text-gray-600 font-medium">
            <div>
              <div class="mb-1">First Name:</div>
              <div class="text-gray-700">{{ studentProfile.user.name }}</div>
            </div>
            <div>
              <div class="mb-1">Last Name:</div>
              <div class="text-gray-700">{{ studentProfile.user.surname }}</div>
            </div>
            <div>
              <div class="mb-1">Email Address:</div>
              <div class="text-gray-700">{{ studentProfile.user.email }}</div>
            </div>
            <div>
              <div class="mb-1">Student ID:</div>
              <div class="text-gray-700">{{ studentProfile.student_id }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Degrees -->
    <div class="mb-4 border p-4 py-6 rounded-xl" *ngIf="studentProfile.degrees && studentProfile.degrees.length > 0">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
        </svg>
        Degree Programs
      </div>

      <div class="space-y-4">
        <div *ngFor="let studentDegree of studentProfile.degrees" class="bg-gray-50 p-4 rounded-lg">
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="font-semibold text-gray-800">{{ studentDegree.degree.name }}</h3>
              <p class="text-sm text-gray-600">{{ studentDegree.degree.description }}</p>
            </div>
            <span class="px-2 py-1 text-xs rounded-full font-medium"
                  [ngClass]="getDegreeStatusColor(studentDegree.status)">
              {{ studentDegree.status | titlecase }}
            </span>
          </div>

          <div class="grid md:grid-cols-3 gap-4 text-xs">
            <div>
              <div class="text-gray-600 mb-1">Level:</div>
              <div class="font-medium">{{ studentDegree.degree.level }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Start Date:</div>
              <div class="font-medium">{{ formatDate(studentDegree.start_date) }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Expected Graduation:</div>
              <div class="font-medium">{{ formatDate(studentDegree.expected_graduation_date) }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Required Credits:</div>
              <div class="font-medium">{{ studentDegree.degree.required_credits }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Minimum GPA:</div>
              <div class="font-medium">{{ studentDegree.degree.min_gpa }}</div>
            </div>
            <div>
              <div class="text-gray-600 mb-1">Final GPA:</div>
              <div class="font-medium">{{ studentDegree.final_gpa || 'In Progress' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enrolled Threads -->
    <div class="mb-4 border p-4 py-6 rounded-xl" *ngIf="studentProfile.enrolled_threads && studentProfile.enrolled_threads.length > 0">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Enrolled Courses
      </div>

      <div class="grid md:grid-cols-2 gap-4">
        <div *ngFor="let thread of studentProfile.enrolled_threads" class="bg-gray-50 p-4 rounded-lg">
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="font-semibold text-gray-800">{{ thread.course.title }}</h3>
              <p class="text-sm text-gray-600 mb-2">{{ thread.title }}</p>
              <p class="text-xs text-gray-500">{{ truncateDescription(thread.course.description, 100) }}</p>
            </div>
          </div>

          <div class="flex justify-between items-center mt-3 pt-3 border-t border-gray-200">
            <div class="text-xs text-gray-600">
              <span class="font-medium">Max Students:</span> {{ thread.max_students }}
            </div>
            <button class="text-xs text-blue-600 hover:text-blue-800 font-medium"
                    [routerLink]="['/thread', thread.id]">
              View Details →
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Courses -->
    <div class="mb-4 border p-4 py-6 rounded-xl" *ngIf="studentProfile.available_courses && studentProfile.available_courses.length > 0">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
        </svg>
        Available Courses
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div *ngFor="let course of studentProfile.available_courses" class="bg-gray-50 p-4 rounded-lg">
          <div class="mb-3">
            <h3 class="font-semibold text-gray-800 mb-2">{{ course.title }}</h3>
            <p class="text-xs text-gray-500">{{ truncateDescription(course.description, 80) }}</p>
          </div>

          <div class="flex justify-between items-center mt-3 pt-3 border-t border-gray-200">
            <div class="text-xs text-gray-600">
              <span class="font-medium">Course ID:</span> {{ course.id }}
            </div>
            <button class="text-xs text-green-600 hover:text-green-800 font-medium"
                    [routerLink]="['/register']">
              Register →
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Summary Statistics -->
    <div class="mb-4 border p-4 py-6 rounded-xl">
      <div class="text-base font-medium text-gray-800 mb-5 flex items-center gap-1">
        <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
        </svg>
        Summary
      </div>

      <div class="grid md:grid-cols-3 gap-4">
        <div class="bg-blue-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-blue-600">{{ studentProfile.summary.total_enrolled_threads }}</div>
          <div class="text-sm text-blue-800">Enrolled Courses</div>
        </div>

        <div class="bg-green-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-green-600">{{ studentProfile.summary.total_available_courses }}</div>
          <div class="text-sm text-green-800">Available Courses</div>
        </div>

        <div class="bg-purple-50 p-4 rounded-lg text-center">
          <div class="text-2xl font-bold text-purple-600">{{ studentProfile.summary.total_degrees }}</div>
          <div class="text-sm text-purple-800">Degree Programs</div>
        </div>
      </div>
    </div>

  </div>
</div>